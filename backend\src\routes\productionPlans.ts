import express from 'express';
import { 
  getProductionPlans, 
  createProductionPlan, 
  getProductionPlan,
  approveProductionPlan,
  startProduction,
  updateProductionPlanStatus,
  deleteProductionPlan
} from '../controllers/productionPlanController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/production-plans - 获取生产计划列表
router.get('/', getProductionPlans);

// GET /api/production-plans/:id - 获取单个生产计划详情
router.get('/:id', getProductionPlan);

// POST /api/production-plans - 创建生产计划
router.post('/', createProductionPlan);

// POST /api/production-plans/:id/approve - 审核生产计划
router.post('/:id/approve', approveProductionPlan);

// POST /api/production-plans/:id/start - 开始生产
router.post('/:id/start', startProduction);

// PUT /api/production-plans/:id/status - 更新生产计划状态
router.put('/:id/status', updateProductionPlanStatus);

// DELETE /api/production-plans/:id - 删除生产计划
router.delete('/:id', deleteProductionPlan);

export default router;
