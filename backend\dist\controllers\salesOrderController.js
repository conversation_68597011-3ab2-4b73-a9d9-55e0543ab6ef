"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSalesOrders = getSalesOrders;
exports.getSalesOrder = getSalesOrder;
exports.createSalesOrder = createSalesOrder;
exports.approveSalesOrder = approveSalesOrder;
exports.updateSalesOrderStatus = updateSalesOrderStatus;
exports.deleteSalesOrder = deleteSalesOrder;
const database_1 = require("../models/database");
// 生成销售订单号
function generateOrderNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getTime()).slice(-6); // 取时间戳后6位
    return `SO${year}${month}${day}${time}`;
}
// 获取销售订单列表
async function getSalesOrders(req, res) {
    try {
        const { page = 1, limit = 10, search = '', status = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE 1=1";
        const params = [];
        if (search) {
            whereClause += " AND (so.order_no LIKE ? OR c.name LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern);
        }
        if (status) {
            whereClause += " AND so.status = ?";
            params.push(status);
        }
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total 
      FROM sales_orders so
      LEFT JOIN customers c ON so.customer_id = c.id
      ${whereClause}
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        so.*,
        c.name as customer_name
      FROM sales_orders so
      LEFT JOIN customers c ON so.customer_id = c.id
      ${whereClause}
      ORDER BY so.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const orders = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: orders,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取销售订单列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取销售订单列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个销售订单详情（包含明细）
async function getSalesOrder(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 获取订单主信息
        const order = await new Promise((resolve, reject) => {
            db.get(`
        SELECT 
          so.*,
          c.name as customer_name
        FROM sales_orders so
        LEFT JOIN customers c ON so.customer_id = c.id
        WHERE so.id = ?
      `, [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!order) {
            return res.status(404).json({
                success: false,
                message: '销售订单不存在'
            });
        }
        // 获取订单明细
        const items = await new Promise((resolve, reject) => {
            db.all(`
        SELECT 
          soi.*,
          p.code as product_code,
          p.name as product_name,
          p.unit
        FROM sales_order_items soi
        LEFT JOIN products p ON soi.product_id = p.id
        WHERE soi.sales_order_id = ?
        ORDER BY soi.id
      `, [id], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const orderWithItems = {
            ...order,
            items
        };
        res.json({
            success: true,
            message: '获取销售订单成功',
            data: orderWithItems
        });
    }
    catch (error) {
        console.error('获取销售订单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建销售订单
async function createSalesOrder(req, res) {
    try {
        const { customer_id, order_date, delivery_date, remark = '', items } = req.body;
        // 验证必填字段
        if (!customer_id || !order_date || !items || items.length === 0) {
            return res.status(400).json({
                success: false,
                message: '客户、订单日期和订单明细不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        const orderNo = generateOrderNo();
        // 计算总金额
        const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
        // 开始事务
        await new Promise((resolve, reject) => {
            db.serialize(async () => {
                try {
                    db.run('BEGIN TRANSACTION');
                    // 创建销售订单主表
                    const orderId = await new Promise((resolve, reject) => {
                        db.run(`
              INSERT INTO sales_orders (
                order_no, customer_id, order_date, delivery_date, 
                total_amount, remark
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [orderNo, customer_id, order_date, delivery_date, totalAmount, remark], function (err) {
                            if (err)
                                reject(err);
                            else
                                resolve(this.lastID);
                        });
                    });
                    // 创建订单明细
                    for (const item of items) {
                        const totalPrice = item.quantity * item.unit_price;
                        await new Promise((resolve, reject) => {
                            db.run(`
                INSERT INTO sales_order_items (
                  sales_order_id, product_id, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
              `, [orderId, item.product_id, item.quantity, item.unit_price, totalPrice], (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        });
                    }
                    db.run('COMMIT', (err) => {
                        if (err)
                            reject(err);
                        else
                            resolve();
                    });
                }
                catch (error) {
                    db.run('ROLLBACK');
                    reject(error);
                }
            });
        });
        res.status(201).json({
            success: true,
            message: '销售订单创建成功',
            data: { order_no: orderNo }
        });
    }
    catch (error) {
        console.error('创建销售订单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 审核销售订单
async function approveSalesOrder(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查订单是否存在且状态为待审核
        const order = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM sales_orders WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!order) {
            return res.status(404).json({
                success: false,
                message: '销售订单不存在'
            });
        }
        if (order.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: '只有待审核状态的订单才能审核'
            });
        }
        // 更新订单状态为已审核
        await new Promise((resolve, reject) => {
            db.run('UPDATE sales_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['approved', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '销售订单审核成功'
        });
    }
    catch (error) {
        console.error('审核销售订单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 更新销售订单状态
async function updateSalesOrderStatus(req, res) {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!status) {
            return res.status(400).json({
                success: false,
                message: '状态不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        await new Promise((resolve, reject) => {
            db.run('UPDATE sales_orders SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [status, id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '销售订单状态更新成功'
        });
    }
    catch (error) {
        console.error('更新销售订单状态错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 删除销售订单
async function deleteSalesOrder(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查订单是否存在
        const order = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM sales_orders WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!order) {
            return res.status(404).json({
                success: false,
                message: '销售订单不存在'
            });
        }
        if (order.status === 'completed') {
            return res.status(400).json({
                success: false,
                message: '已完成的订单不能删除'
            });
        }
        // 开始事务删除订单和明细
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                db.run('BEGIN TRANSACTION');
                // 删除订单明细
                db.run('DELETE FROM sales_order_items WHERE sales_order_id = ?', [id], (err) => {
                    if (err) {
                        db.run('ROLLBACK');
                        reject(err);
                        return;
                    }
                    // 删除订单主表
                    db.run('DELETE FROM sales_orders WHERE id = ?', [id], (err) => {
                        if (err) {
                            db.run('ROLLBACK');
                            reject(err);
                        }
                        else {
                            db.run('COMMIT', (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        }
                    });
                });
            });
        });
        res.json({
            success: true,
            message: '销售订单删除成功'
        });
    }
    catch (error) {
        console.error('删除销售订单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=salesOrderController.js.map