"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSuppliers = getSuppliers;
exports.getSupplier = getSupplier;
exports.createSupplier = createSupplier;
exports.updateSupplier = updateSupplier;
exports.deleteSupplier = deleteSupplier;
const database_1 = require("../models/database");
// 获取供应商列表
async function getSuppliers(req, res) {
    try {
        const { page = 1, limit = 10, search = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE status = 'active'";
        const params = [];
        if (search) {
            whereClause += " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM suppliers ${whereClause}`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT * FROM suppliers ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
        const suppliers = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: suppliers,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取供应商列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取供应商列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个供应商
async function getSupplier(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        const supplier = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!supplier) {
            return res.status(404).json({
                success: false,
                message: '供应商不存在'
            });
        }
        res.json({
            success: true,
            message: '获取供应商成功',
            data: supplier
        });
    }
    catch (error) {
        console.error('获取供应商错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建供应商
async function createSupplier(req, res) {
    try {
        const { code, name, contact_person = '', phone = '', address = '', settlement_method = '' } = req.body;
        // 验证必填字段
        if (!code || !name) {
            return res.status(400).json({
                success: false,
                message: '供应商编码和名称不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 检查编码是否已存在
        const existingSupplier = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM suppliers WHERE code = ?', [code], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (existingSupplier) {
            return res.status(409).json({
                success: false,
                message: '供应商编码已存在'
            });
        }
        // 创建供应商
        const supplierId = await new Promise((resolve, reject) => {
            db.run(`INSERT INTO suppliers (code, name, contact_person, phone, address, settlement_method)
         VALUES (?, ?, ?, ?, ?, ?)`, [code, name, contact_person, phone, address, settlement_method], function (err) {
                if (err)
                    reject(err);
                else
                    resolve(this.lastID);
            });
        });
        res.status(201).json({
            success: true,
            message: '供应商创建成功',
            data: { id: supplierId }
        });
    }
    catch (error) {
        console.error('创建供应商错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 更新供应商
async function updateSupplier(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const db = (0, database_1.getDatabase)();
        // 检查供应商是否存在
        const existingSupplier = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingSupplier) {
            return res.status(404).json({
                success: false,
                message: '供应商不存在'
            });
        }
        // 如果更新编码，检查是否重复
        if (updateData.code && updateData.code !== existingSupplier.code) {
            const duplicateSupplier = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM suppliers WHERE code = ? AND id != ?', [updateData.code, id], (err, row) => {
                    if (err)
                        reject(err);
                    else
                        resolve(row);
                });
            });
            if (duplicateSupplier) {
                return res.status(409).json({
                    success: false,
                    message: '供应商编码已存在'
                });
            }
        }
        // 构建更新语句
        const updateFields = [];
        const updateValues = [];
        Object.entries(updateData).forEach(([key, value]) => {
            if (value !== undefined) {
                updateFields.push(`${key} = ?`);
                updateValues.push(value);
            }
        });
        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有提供更新数据'
            });
        }
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateValues.push(id);
        await new Promise((resolve, reject) => {
            db.run(`UPDATE suppliers SET ${updateFields.join(', ')} WHERE id = ?`, updateValues, (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '供应商更新成功'
        });
    }
    catch (error) {
        console.error('更新供应商错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 删除供应商（软删除）
async function deleteSupplier(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查供应商是否存在
        const existingSupplier = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM suppliers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingSupplier) {
            return res.status(404).json({
                success: false,
                message: '供应商不存在'
            });
        }
        // 软删除
        await new Promise((resolve, reject) => {
            db.run('UPDATE suppliers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['inactive', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '供应商删除成功'
        });
    }
    catch (error) {
        console.error('删除供应商错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=supplierController.js.map