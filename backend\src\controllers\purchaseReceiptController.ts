import { Request, Response } from 'express';
import { getDatabase } from '../models/database';
import {
  PurchaseReceipt,
  PurchaseReceiptItem,
  PurchaseReceiptCreateInput,
  ApiResponse,
  PaginatedResponse
} from '../types';
import { updateInventoryWithMovement } from '../utils/inventoryUtils';

// 生成入库单号
function generateReceiptNo(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const time = String(now.getTime()).slice(-6); // 取时间戳后6位
  return `PR${year}${month}${day}${time}`;
}

// 获取采购入库单列表
export async function getPurchaseReceipts(req: Request, res: Response) {
  try {
    const { page = 1, limit = 10, search = '', status = '' } = req.query;
    const offset = (Number(page) - 1) * Number(limit);
    
    const db = getDatabase();
    
    // 构建查询条件
    let whereClause = "WHERE 1=1";
    const params: any[] = [];
    
    if (search) {
      whereClause += " AND (pr.receipt_no LIKE ? OR s.name LIKE ? OR po.order_no LIKE ?)";
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }
    
    if (status) {
      whereClause += " AND pr.status = ?";
      params.push(status);
    }
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM purchase_receipts pr
      LEFT JOIN suppliers s ON pr.supplier_id = s.id
      LEFT JOIN purchase_orders po ON pr.purchase_order_id = po.id
      ${whereClause}
    `;
    const totalResult = await new Promise<{ total: number }>((resolve, reject) => {
      db.get(countQuery, params, (err, row) => {
        if (err) reject(err);
        else resolve(row as { total: number });
      });
    });
    
    // 获取数据
    const dataQuery = `
      SELECT 
        pr.*,
        s.name as supplier_name,
        po.order_no as purchase_order_no
      FROM purchase_receipts pr
      LEFT JOIN suppliers s ON pr.supplier_id = s.id
      LEFT JOIN purchase_orders po ON pr.purchase_order_id = po.id
      ${whereClause}
      ORDER BY pr.created_at DESC
      LIMIT ? OFFSET ?
    `;
    const receipts = await new Promise<PurchaseReceipt[]>((resolve, reject) => {
      db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as PurchaseReceipt[]);
      });
    });
    
    const response: PaginatedResponse<PurchaseReceipt> = {
      data: receipts,
      total: totalResult.total,
      page: Number(page),
      limit: Number(limit),
      totalPages: Math.ceil(totalResult.total / Number(limit))
    };
    
    res.json({
      success: true,
      message: '获取采购入库单列表成功',
      data: response
    } as ApiResponse<PaginatedResponse<PurchaseReceipt>>);
    
  } catch (error) {
    console.error('获取采购入库单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 获取单个采购入库单详情（包含明细）
export async function getPurchaseReceipt(req: Request, res: Response) {
  try {
    const { id } = req.params;
    const db = getDatabase();
    
    // 获取入库单主信息
    const receipt = await new Promise<PurchaseReceipt | undefined>((resolve, reject) => {
      db.get(`
        SELECT 
          pr.*,
          s.name as supplier_name,
          po.order_no as purchase_order_no
        FROM purchase_receipts pr
        LEFT JOIN suppliers s ON pr.supplier_id = s.id
        LEFT JOIN purchase_orders po ON pr.purchase_order_id = po.id
        WHERE pr.id = ?
      `, [id], (err, row) => {
        if (err) reject(err);
        else resolve(row as PurchaseReceipt);
      });
    });
    
    if (!receipt) {
      return res.status(404).json({
        success: false,
        message: '采购入库单不存在'
      } as ApiResponse);
    }
    
    // 获取入库单明细
    const items = await new Promise<PurchaseReceiptItem[]>((resolve, reject) => {
      db.all(`
        SELECT 
          pri.*,
          m.code as material_code,
          m.name as material_name,
          m.unit
        FROM purchase_receipt_items pri
        LEFT JOIN materials m ON pri.material_id = m.id
        WHERE pri.purchase_receipt_id = ?
        ORDER BY pri.id
      `, [id], (err, rows) => {
        if (err) reject(err);
        else resolve(rows as PurchaseReceiptItem[]);
      });
    });
    
    const receiptWithItems = {
      ...receipt,
      items
    };
    
    res.json({
      success: true,
      message: '获取采购入库单成功',
      data: receiptWithItems
    } as ApiResponse);
    
  } catch (error) {
    console.error('获取采购入库单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}

// 创建采购入库单
export async function createPurchaseReceipt(req: Request, res: Response) {
  try {
    const {
      purchase_order_id,
      receipt_date,
      remark = '',
      items
    }: PurchaseReceiptCreateInput = req.body;
    
    // 验证必填字段
    if (!purchase_order_id || !receipt_date || !items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: '采购订单、入库日期和入库明细不能为空'
      } as ApiResponse);
    }
    
    const db = getDatabase();
    
    // 检查采购订单是否存在且已审核
    const purchaseOrder = await new Promise<any>((resolve, reject) => {
      db.get(`
        SELECT po.*, s.id as supplier_id 
        FROM purchase_orders po
        LEFT JOIN suppliers s ON po.supplier_id = s.id
        WHERE po.id = ?
      `, [purchase_order_id], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: '采购订单不存在'
      } as ApiResponse);
    }
    
    if (purchaseOrder.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: '只有已审核的采购订单才能创建入库单'
      } as ApiResponse);
    }
    
    const receiptNo = generateReceiptNo();
    
    // 计算总金额
    const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    
    // 开始事务
    await new Promise<void>((resolve, reject) => {
      db.serialize(async () => {
        try {
          db.run('BEGIN TRANSACTION');
          
          // 创建采购入库单主表
          const receiptId = await new Promise<number>((resolve, reject) => {
            db.run(`
              INSERT INTO purchase_receipts (
                receipt_no, purchase_order_id, supplier_id, receipt_date, 
                total_amount, remark
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [receiptNo, purchase_order_id, purchaseOrder.supplier_id, receipt_date, totalAmount, remark], 
            function(err) {
              if (err) reject(err);
              else resolve(this.lastID);
            });
          });
          
          // 创建入库单明细并更新原材料库存
          for (const item of items) {
            const totalPrice = item.quantity * item.unit_price;
            
            // 创建入库单明细
            await new Promise<void>((resolve, reject) => {
              db.run(`
                INSERT INTO purchase_receipt_items (
                  purchase_receipt_id, material_id, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
              `, [receiptId, item.material_id, item.quantity, item.unit_price, totalPrice],
              (err) => {
                if (err) reject(err);
                else resolve();
              });
            });
            
            // 更新原材料库存并记录变动
            await updateInventoryWithMovement(
              'material',
              item.material_id,
              item.quantity,
              'in',
              'purchase_receipt',
              receiptId,
              receiptNo,
              `采购入库：${item.quantity}`,
              (req as any).user?.id
            );
          }
          
          db.run('COMMIT', (err) => {
            if (err) reject(err);
            else resolve();
          });
          
        } catch (error) {
          db.run('ROLLBACK');
          reject(error);
        }
      });
    });
    
    res.status(201).json({
      success: true,
      message: '采购入库单创建成功，库存已更新',
      data: { receipt_no: receiptNo }
    } as ApiResponse);
    
  } catch (error) {
    console.error('创建采购入库单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    } as ApiResponse);
  }
}
