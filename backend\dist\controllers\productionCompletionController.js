"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProductionCompletions = getProductionCompletions;
exports.getProductionCompletion = getProductionCompletion;
exports.createProductionCompletion = createProductionCompletion;
const database_1 = require("../models/database");
// 生成完工单号
function generateCompletionNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getTime()).slice(-6); // 取时间戳后6位
    return `PC${year}${month}${day}${time}`;
}
// 获取生产完工单列表
async function getProductionCompletions(req, res) {
    try {
        const { page = 1, limit = 10, search = '', status = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE 1=1";
        const params = [];
        if (search) {
            whereClause += " AND (pc.completion_no LIKE ? OR p.name LIKE ? OR pp.plan_no LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        if (status) {
            whereClause += " AND pc.status = ?";
            params.push(status);
        }
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total 
      FROM production_completions pc
      LEFT JOIN products p ON pc.product_id = p.id
      LEFT JOIN production_plans pp ON pc.production_plan_id = pp.id
      ${whereClause}
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        pc.*,
        p.code as product_code,
        p.name as product_name,
        pp.plan_no as production_plan_no
      FROM production_completions pc
      LEFT JOIN products p ON pc.product_id = p.id
      LEFT JOIN production_plans pp ON pc.production_plan_id = pp.id
      ${whereClause}
      ORDER BY pc.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const completions = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: completions,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取生产完工单列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取生产完工单列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个生产完工单详情（包含物料明细）
async function getProductionCompletion(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 获取完工单主信息
        const completion = await new Promise((resolve, reject) => {
            db.get(`
        SELECT 
          pc.*,
          p.code as product_code,
          p.name as product_name,
          pp.plan_no as production_plan_no
        FROM production_completions pc
        LEFT JOIN products p ON pc.product_id = p.id
        LEFT JOIN production_plans pp ON pc.production_plan_id = pp.id
        WHERE pc.id = ?
      `, [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!completion) {
            return res.status(404).json({
                success: false,
                message: '生产完工单不存在'
            });
        }
        // 获取物料消耗明细
        const items = await new Promise((resolve, reject) => {
            db.all(`
        SELECT 
          pci.*,
          m.code as material_code,
          m.name as material_name,
          m.unit
        FROM production_completion_items pci
        LEFT JOIN materials m ON pci.material_id = m.id
        WHERE pci.production_completion_id = ?
        ORDER BY pci.id
      `, [id], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const completionWithItems = {
            ...completion,
            items
        };
        res.json({
            success: true,
            message: '获取生产完工单成功',
            data: completionWithItems
        });
    }
    catch (error) {
        console.error('获取生产完工单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建生产完工单
async function createProductionCompletion(req, res) {
    try {
        const { production_plan_id, completed_quantity, completion_date, remark = '', materials } = req.body;
        // 验证必填字段
        if (!production_plan_id || !completed_quantity || !completion_date || !materials || materials.length === 0) {
            return res.status(400).json({
                success: false,
                message: '生产计划、完工数量、完工日期和物料消耗不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 检查生产计划是否存在且状态为进行中
        const productionPlan = await new Promise((resolve, reject) => {
            db.get(`
        SELECT pp.*, p.id as product_id 
        FROM production_plans pp
        LEFT JOIN products p ON pp.product_id = p.id
        WHERE pp.id = ?
      `, [production_plan_id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!productionPlan) {
            return res.status(404).json({
                success: false,
                message: '生产计划不存在'
            });
        }
        if (productionPlan.status !== 'in_progress') {
            return res.status(400).json({
                success: false,
                message: '只有进行中的生产计划才能创建完工单'
            });
        }
        // 检查原材料库存是否充足
        for (const material of materials) {
            const materialInfo = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM materials WHERE id = ?', [material.material_id], (err, row) => {
                    if (err)
                        reject(err);
                    else
                        resolve(row);
                });
            });
            if (!materialInfo) {
                return res.status(404).json({
                    success: false,
                    message: `原材料ID ${material.material_id} 不存在`
                });
            }
            if (materialInfo.current_stock < material.consumed_quantity) {
                return res.status(400).json({
                    success: false,
                    message: `原材料 "${materialInfo.name}" 库存不足，当前库存：${materialInfo.current_stock}，需要：${material.consumed_quantity}`
                });
            }
        }
        const completionNo = generateCompletionNo();
        // 开始事务
        await new Promise((resolve, reject) => {
            db.serialize(async () => {
                try {
                    db.run('BEGIN TRANSACTION');
                    // 创建生产完工单主表
                    const completionId = await new Promise((resolve, reject) => {
                        db.run(`
              INSERT INTO production_completions (
                completion_no, production_plan_id, product_id, completed_quantity, 
                completion_date, remark
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [completionNo, production_plan_id, productionPlan.product_id, completed_quantity, completion_date, remark], function (err) {
                            if (err)
                                reject(err);
                            else
                                resolve(this.lastID);
                        });
                    });
                    // 创建物料消耗明细并更新库存
                    for (const material of materials) {
                        // 创建消耗明细
                        await new Promise((resolve, reject) => {
                            db.run(`
                INSERT INTO production_completion_items (
                  production_completion_id, material_id, consumed_quantity
                ) VALUES (?, ?, ?)
              `, [completionId, material.material_id, material.consumed_quantity], (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        });
                        // 更新原材料库存（减少）
                        await new Promise((resolve, reject) => {
                            db.run(`
                UPDATE materials 
                SET current_stock = current_stock - ?, updated_at = CURRENT_TIMESTAMP 
                WHERE id = ?
              `, [material.consumed_quantity, material.material_id], (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        });
                    }
                    // 更新成品库存（增加）
                    await new Promise((resolve, reject) => {
                        db.run(`
              UPDATE products 
              SET current_stock = current_stock + ?, updated_at = CURRENT_TIMESTAMP 
              WHERE id = ?
            `, [completed_quantity, productionPlan.product_id], (err) => {
                            if (err)
                                reject(err);
                            else
                                resolve();
                        });
                    });
                    // 更新生产计划的实际完成数量
                    await new Promise((resolve, reject) => {
                        db.run(`
              UPDATE production_plans 
              SET actual_quantity = actual_quantity + ?, updated_at = CURRENT_TIMESTAMP 
              WHERE id = ?
            `, [completed_quantity, production_plan_id], (err) => {
                            if (err)
                                reject(err);
                            else
                                resolve();
                        });
                    });
                    db.run('COMMIT', (err) => {
                        if (err)
                            reject(err);
                        else
                            resolve();
                    });
                }
                catch (error) {
                    db.run('ROLLBACK');
                    reject(error);
                }
            });
        });
        res.status(201).json({
            success: true,
            message: '生产完工单创建成功，库存已更新',
            data: { completion_no: completionNo }
        });
    }
    catch (error) {
        console.error('创建生产完工单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=productionCompletionController.js.map