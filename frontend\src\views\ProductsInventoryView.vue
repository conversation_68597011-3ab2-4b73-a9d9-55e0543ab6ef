<template>
  <div class="products-inventory-view">
    <div class="page-header">
      <h1>成品库存</h1>
      <p>查看成品库存状态和变动历史</p>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入编码、名称或规格"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="库存状态">
          <el-select
            v-model="searchForm.alert_status"
            placeholder="请选择库存状态"
            clearable
            style="width: 150px"
          >
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="低库存" value="low_stock" />
            <el-option label="高库存" value="high_stock" />
            <el-option label="零库存" value="zero_stock" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 库存列表 -->
    <el-card class="table-card">
      <DataTable
        :data="tableData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      >
        <template #alert_status="{ row }">
          <el-tag :type="getAlertStatusColor(row.alert_status)">
            {{ getAlertStatusText(row.alert_status) }}
          </el-tag>
        </template>
        
        <template #current_stock="{ row }">
          <span :class="getStockClass(row)">
            {{ row.current_stock }}
          </span>
        </template>
        
        <template #actions="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="viewHistory(row)"
          >
            变动历史
          </el-button>
        </template>
      </DataTable>
    </el-card>

    <!-- 库存变动历史对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      :title="`${selectedItem?.name} - 库存变动历史`"
      width="80%"
      top="5vh"
    >
      <el-table
        :data="historyData"
        v-loading="historyLoading"
        style="width: 100%"
      >
        <el-table-column prop="movement_type" label="变动类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getMovementTypeColor(row.movement_type)">
              {{ getMovementTypeText(row.movement_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="变动数量" width="120">
          <template #default="{ row }">
            <span :class="row.movement_type === 'in' ? 'text-success' : 'text-danger'">
              {{ row.movement_type === 'in' ? '+' : '-' }}{{ Math.abs(row.quantity) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="before_quantity" label="变动前" width="100" />
        <el-table-column prop="after_quantity" label="变动后" width="100" />
        <el-table-column prop="reference_no" label="关联单据" width="150" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column prop="created_by_name" label="操作人" width="100" />
        <el-table-column prop="created_at" label="变动时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
      
      <div class="dialog-pagination">
        <el-pagination
          v-model:current-page="historyPagination.page"
          v-model:page-size="historyPagination.limit"
          :page-sizes="[10, 20, 50]"
          :total="historyPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryPageChange"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import DataTable from '@/components/DataTable.vue'
import { inventoryApi, type InventoryItem, type InventoryMovement } from '@/api/inventory'

// 搜索表单
const searchForm = reactive({
  search: '',
  alert_status: ''
})

// 表格数据
const tableData = ref<InventoryItem[]>([])
const loading = ref(false)

// 分页
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 表格列配置
const columns = [
  { prop: 'code', label: '编码', width: 120 },
  { prop: 'name', label: '名称', width: 200 },
  { prop: 'specification', label: '规格', width: 150 },
  { prop: 'unit', label: '单位', width: 80 },
  { prop: 'current_stock', label: '当前库存', width: 120, slot: 'current_stock' },
  { prop: 'stock_min', label: '最低库存', width: 100 },
  { prop: 'stock_max', label: '最高库存', width: 100 },
  { prop: 'cost_price', label: '成本价', width: 100 },
  { prop: 'sale_price', label: '销售价', width: 100 },
  { prop: 'alert_status', label: '库存状态', width: 120, slot: 'alert_status' },
  { prop: 'actions', label: '操作', width: 120, slot: 'actions', fixed: 'right' }
]

// 库存变动历史
const historyDialogVisible = ref(false)
const historyData = ref<InventoryMovement[]>([])
const historyLoading = ref(false)
const selectedItem = ref<InventoryItem | null>(null)

const historyPagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      search: searchForm.search || undefined,
      alert_status: searchForm.alert_status || undefined
    }
    
    const res = await inventoryApi.getProductsInventory(params)
    tableData.value = res.data.data.data
    pagination.total = res.data.data.total
  } catch (error) {
    console.error('获取成品库存失败:', error)
    ElMessage.error('获取成品库存失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  searchForm.search = ''
  searchForm.alert_status = ''
  pagination.page = 1
  fetchData()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchData()
}

// 查看变动历史
const viewHistory = async (item: InventoryItem) => {
  selectedItem.value = item
  historyDialogVisible.value = true
  historyPagination.page = 1
  await fetchHistoryData()
}

// 获取变动历史数据
const fetchHistoryData = async () => {
  if (!selectedItem.value) return
  
  historyLoading.value = true
  try {
    const res = await inventoryApi.getInventoryHistory(
      'product',
      selectedItem.value.id,
      {
        page: historyPagination.page,
        limit: historyPagination.limit
      }
    )
    historyData.value = res.data.data.data
    historyPagination.total = res.data.data.total
  } catch (error) {
    console.error('获取变动历史失败:', error)
    ElMessage.error('获取变动历史失败')
  } finally {
    historyLoading.value = false
  }
}

// 历史记录分页处理
const handleHistoryPageChange = (page: number) => {
  historyPagination.page = page
  fetchHistoryData()
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.limit = size
  historyPagination.page = 1
  fetchHistoryData()
}

// 工具函数
const getAlertStatusColor = (status: string) => {
  switch (status) {
    case 'zero_stock': return 'danger'
    case 'low_stock': return 'warning'
    case 'high_stock': return 'info'
    default: return 'success'
  }
}

const getAlertStatusText = (status: string) => {
  switch (status) {
    case 'zero_stock': return '零库存'
    case 'low_stock': return '低库存'
    case 'high_stock': return '高库存'
    default: return '正常'
  }
}

const getStockClass = (row: InventoryItem) => {
  switch (row.alert_status) {
    case 'zero_stock': return 'text-danger'
    case 'low_stock': return 'text-warning'
    case 'high_stock': return 'text-info'
    default: return 'text-success'
  }
}

const getMovementTypeColor = (type: string) => {
  switch (type) {
    case 'in': return 'success'
    case 'out': return 'danger'
    case 'adjust': return 'warning'
    default: return 'info'
  }
}

const getMovementTypeText = (type: string) => {
  switch (type) {
    case 'in': return '入库'
    case 'out': return '出库'
    case 'adjust': return '调整'
    default: return type
  }
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.products-inventory-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 16px;
}

.table-card {
  margin-bottom: 24px;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

.text-info {
  color: #909399;
}

.dialog-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
