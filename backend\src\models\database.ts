import sqlite3 from 'sqlite3';
import path from 'path';

// 数据库实例
let db: sqlite3.Database;

// 获取数据库实例
export function getDatabase(): sqlite3.Database {
  if (!db) {
    throw new Error('数据库未初始化');
  }
  return db;
}

// 初始化数据库
export async function initDatabase(): Promise<void> {
  return new Promise((resolve, reject) => {
    const dbPath = path.join(__dirname, '../../data/erp.db');
    
    // 确保数据目录存在
    const fs = require('fs');
    const dataDir = path.dirname(dbPath);
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }

    db = new sqlite3.Database(dbPath, (err) => {
      if (err) {
        reject(err);
        return;
      }

      console.log('连接到SQLite数据库');

      // 设置UTF-8编码
      db.run("PRAGMA encoding = 'UTF-8'", (err) => {
        if (err) {
          console.error('设置编码失败:', err);
        }
      });

      createTables()
        .then(() => resolve())
        .catch(reject);
    });
  });
}

// 创建数据表
async function createTables(): Promise<void> {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // 用户表
      db.run(`
        CREATE TABLE IF NOT EXISTS users (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          username VARCHAR(50) UNIQUE NOT NULL,
          password VARCHAR(255) NOT NULL,
          email VARCHAR(100),
          role VARCHAR(20) DEFAULT 'user',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 原材料表
      db.run(`
        CREATE TABLE IF NOT EXISTS materials (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 成品表
      db.run(`
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          specification VARCHAR(200),
          unit VARCHAR(20) NOT NULL,
          cost_price DECIMAL(10,2) DEFAULT 0,
          sale_price DECIMAL(10,2) DEFAULT 0,
          stock_min INTEGER DEFAULT 0,
          stock_max INTEGER DEFAULT 0,
          current_stock INTEGER DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 供应商表
      db.run(`
        CREATE TABLE IF NOT EXISTS suppliers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          contact_person VARCHAR(50),
          phone VARCHAR(20),
          address VARCHAR(200),
          settlement_method VARCHAR(50),
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 客户表
      db.run(`
        CREATE TABLE IF NOT EXISTS customers (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          code VARCHAR(50) UNIQUE NOT NULL,
          name VARCHAR(100) NOT NULL,
          contact_person VARCHAR(50),
          phone VARCHAR(20),
          address VARCHAR(200),
          credit_limit DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'active',
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 采购订单表
      db.run(`
        CREATE TABLE IF NOT EXISTS purchase_orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_no VARCHAR(50) UNIQUE NOT NULL,
          supplier_id INTEGER NOT NULL,
          order_date DATE NOT NULL,
          expected_date DATE,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )
      `);

      // 采购订单明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS purchase_order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          purchase_order_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          received_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);

      // 采购入库单表
      db.run(`
        CREATE TABLE IF NOT EXISTS purchase_receipts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          receipt_no VARCHAR(50) UNIQUE NOT NULL,
          purchase_order_id INTEGER NOT NULL,
          supplier_id INTEGER NOT NULL,
          receipt_date DATE NOT NULL,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
          FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
        )
      `);

      // 采购入库单明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS purchase_receipt_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          purchase_receipt_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (purchase_receipt_id) REFERENCES purchase_receipts(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);

      // 销售订单表
      db.run(`
        CREATE TABLE IF NOT EXISTS sales_orders (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          order_no VARCHAR(50) UNIQUE NOT NULL,
          customer_id INTEGER NOT NULL,
          order_date DATE NOT NULL,
          delivery_date DATE,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (customer_id) REFERENCES customers(id)
        )
      `);

      // 销售订单明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS sales_order_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          sales_order_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          delivered_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);

      // 销售出库单表
      db.run(`
        CREATE TABLE IF NOT EXISTS sales_deliveries (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          delivery_no VARCHAR(50) UNIQUE NOT NULL,
          sales_order_id INTEGER NOT NULL,
          customer_id INTEGER NOT NULL,
          delivery_date DATE NOT NULL,
          total_amount DECIMAL(10,2) DEFAULT 0,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
          FOREIGN KEY (customer_id) REFERENCES customers(id)
        )
      `);

      // 销售出库单明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS sales_delivery_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          sales_delivery_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          quantity DECIMAL(10,2) NOT NULL,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (sales_delivery_id) REFERENCES sales_deliveries(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);

      // 生产计划表
      db.run(`
        CREATE TABLE IF NOT EXISTS production_plans (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          plan_no VARCHAR(50) UNIQUE NOT NULL,
          product_id INTEGER NOT NULL,
          planned_quantity DECIMAL(10,2) NOT NULL,
          actual_quantity DECIMAL(10,2) DEFAULT 0,
          plan_date DATE NOT NULL,
          start_date DATE,
          completion_date DATE,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);

      // 生产计划物料明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS production_plan_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          production_plan_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          required_quantity DECIMAL(10,2) NOT NULL,
          consumed_quantity DECIMAL(10,2) DEFAULT 0,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_plan_id) REFERENCES production_plans(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `);

      // 生产完工表
      db.run(`
        CREATE TABLE IF NOT EXISTS production_completions (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          completion_no VARCHAR(50) UNIQUE NOT NULL,
          production_plan_id INTEGER NOT NULL,
          product_id INTEGER NOT NULL,
          completed_quantity DECIMAL(10,2) NOT NULL,
          completion_date DATE NOT NULL,
          status VARCHAR(20) DEFAULT 'draft',
          remark TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_plan_id) REFERENCES production_plans(id),
          FOREIGN KEY (product_id) REFERENCES products(id)
        )
      `);

      // 生产完工物料消耗明细表
      db.run(`
        CREATE TABLE IF NOT EXISTS production_completion_items (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          production_completion_id INTEGER NOT NULL,
          material_id INTEGER NOT NULL,
          consumed_quantity DECIMAL(10,2) NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (production_completion_id) REFERENCES production_completions(id),
          FOREIGN KEY (material_id) REFERENCES materials(id)
        )
      `, async (err) => {
        if (err) {
          reject(err);
        } else {
          console.log('数据表创建成功');
          try {
            await createDefaultUser();
            resolve();
          } catch (error) {
            reject(error);
          }
        }
      });
    });
  });
}

// 创建默认用户
async function createDefaultUser(): Promise<void> {
  return new Promise((resolve, reject) => {
    // 检查是否已存在admin用户
    db.get('SELECT * FROM users WHERE username = ?', ['admin'], async (err, row) => {
      if (err) {
        reject(err);
        return;
      }

      if (!row) {
        // 如果不存在admin用户，则创建
        const bcrypt = require('bcryptjs');
        const hashedPassword = await bcrypt.hash('123456', 10);

        db.run(
          'INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)',
          ['admin', hashedPassword, '<EMAIL>', 'admin'],
          (err) => {
            if (err) {
              reject(err);
            } else {
              console.log('默认admin用户创建成功');
              resolve();
            }
          }
        );
      } else {
        console.log('admin用户已存在');
        resolve();
      }
    });
  });
}

// 关闭数据库连接
export function closeDatabase(): void {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('关闭数据库时出错:', err);
      } else {
        console.log('数据库连接已关闭');
      }
    });
  }
}
