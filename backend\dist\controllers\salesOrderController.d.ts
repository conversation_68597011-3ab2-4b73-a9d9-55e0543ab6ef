import { Request, Response } from 'express';
export declare function getSalesOrders(req: Request, res: Response): Promise<void>;
export declare function getSalesOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createSalesOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function approveSalesOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updateSalesOrderStatus(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function deleteSalesOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=salesOrderController.d.ts.map