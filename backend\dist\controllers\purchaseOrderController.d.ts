import { Request, Response } from 'express';
export declare function getPurchaseOrders(req: Request, res: Response): Promise<void>;
export declare function getPurchaseOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createPurchaseOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function approvePurchaseOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updatePurchaseOrderStatus(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function deletePurchaseOrder(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=purchaseOrderController.d.ts.map