"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const supplierController_1 = require("../controllers/supplierController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/suppliers - 获取供应商列表
router.get('/', supplierController_1.getSuppliers);
// GET /api/suppliers/:id - 获取单个供应商详情
router.get('/:id', supplierController_1.getSupplier);
// POST /api/suppliers - 创建供应商
router.post('/', supplierController_1.createSupplier);
// PUT /api/suppliers/:id - 更新供应商
router.put('/:id', supplierController_1.updateSupplier);
// DELETE /api/suppliers/:id - 删除供应商
router.delete('/:id', supplierController_1.deleteSupplier);
exports.default = router;
//# sourceMappingURL=suppliers.js.map