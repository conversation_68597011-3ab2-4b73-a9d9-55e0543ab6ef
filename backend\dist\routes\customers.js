"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const customerController_1 = require("../controllers/customerController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/customers - 获取客户列表
router.get('/', customerController_1.getCustomers);
// GET /api/customers/:id - 获取单个客户详情
router.get('/:id', customerController_1.getCustomer);
// POST /api/customers - 创建客户
router.post('/', customerController_1.createCustomer);
// PUT /api/customers/:id - 更新客户
router.put('/:id', customerController_1.updateCustomer);
// DELETE /api/customers/:id - 删除客户
router.delete('/:id', customerController_1.deleteCustomer);
exports.default = router;
//# sourceMappingURL=customers.js.map