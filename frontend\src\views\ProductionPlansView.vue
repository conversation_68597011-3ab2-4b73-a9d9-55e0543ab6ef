<template>
  <div class="production-plans-container">
    <!-- 页面标题 -->
    <div class="page-title">
      <h1>生产计划管理</h1>
    </div>

    <!-- 操作栏 -->
    <div class="operations-bar">
      <div class="operations-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索计划号或成品"
          :style="{ width: isMobile ? '100%' : '300px', maxWidth: '400px' }"
          clearable
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px; margin-left: 10px;" @change="handleSearch">
          <el-option label="全部" value="" />
          <el-option label="草稿" value="draft" />
          <el-option label="已审核" value="approved" />
          <el-option label="进行中" value="in_progress" />
          <el-option label="已完成" value="completed" />
          <el-option label="已取消" value="cancelled" />
        </el-select>
      </div>
      <div class="operations-right">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增生产计划
        </el-button>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
      <el-table
        v-loading="loading"
        :data="productionPlans"
        style="width: 100%"
        stripe
        :class="{ 'mobile-table': isMobile }"
      >
        <el-table-column prop="plan_no" label="计划号" :min-width="isMobile ? 120 : 150" />
        <el-table-column prop="product_name" label="成品" :min-width="isMobile ? 100 : 120" />
        <el-table-column v-if="!isMobile" prop="plan_date" label="计划日期" min-width="100" />
        <el-table-column prop="planned_quantity" label="计划数量" :min-width="isMobile ? 90 : 100" />
        <el-table-column v-if="!isMobile" prop="actual_quantity" label="完成数量" min-width="100" />
        <el-table-column prop="status" label="状态" :min-width="isMobile ? 80 : 100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" :min-width="isMobile ? 150 : 200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewPlan(row)">查看</el-button>
            <el-button 
              v-if="row.status === 'draft'" 
              type="success" 
              size="small" 
              @click="approvePlan(row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="row.status === 'approved'" 
              type="warning" 
              size="small" 
              @click="startPlan(row)"
            >
              开始
            </el-button>
            <el-button 
              v-if="row.status !== 'completed'" 
              type="danger" 
              size="small" 
              @click="deletePlan(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增生产计划"
      width="80%"
      :close-on-click-modal="false"
    >
      <el-form
        ref="planFormRef"
        :model="planForm"
        :rules="planRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="成品" prop="product_id">
              <el-select v-model="planForm.product_id" placeholder="请选择成品" style="width: 100%">
                <el-option
                  v-for="product in products"
                  :key="product.id"
                  :label="product.name"
                  :value="product.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划数量" prop="planned_quantity">
              <el-input-number v-model="planForm.planned_quantity" :min="1" :precision="2" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="计划日期" prop="plan_date">
              <el-date-picker
                v-model="planForm.plan_date"
                type="date"
                placeholder="选择计划日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="备注">
              <el-input v-model="planForm.remark" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 物料清单 -->
      <div class="materials-section">
        <div class="section-header">
          <h3>物料清单</h3>
          <el-button type="primary" size="small" @click="addMaterial">
            <el-icon><Plus /></el-icon>
            添加物料
          </el-button>
        </div>
        
        <el-table :data="planForm.materials" style="width: 100%">
          <el-table-column label="原材料" min-width="150">
            <template #default="{ row, $index }">
              <el-select 
                v-model="row.material_id" 
                placeholder="选择原材料" 
                style="width: 100%"
              >
                <el-option
                  v-for="material in materials"
                  :key="material.id"
                  :label="`${material.code} - ${material.name}`"
                  :value="material.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="需要数量" width="150">
            <template #default="{ row, $index }">
              <el-input-number 
                v-model="row.required_quantity" 
                :min="0" 
                :precision="2" 
                style="width: 100%"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template #default="{ $index }">
              <el-button type="danger" size="small" @click="removeMaterial($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialog">取消</el-button>
          <el-button type="primary" :loading="submitLoading" @click="submitPlan">
            {{ submitLoading ? '保存中...' : '保存' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 计划详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="生产计划详情" width="80%">
      <div v-if="currentPlan">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="计划号">{{ currentPlan.plan_no }}</el-descriptions-item>
          <el-descriptions-item label="成品">{{ currentPlan.product_name }}</el-descriptions-item>
          <el-descriptions-item label="计划数量">{{ currentPlan.planned_quantity }}</el-descriptions-item>
          <el-descriptions-item label="完成数量">{{ currentPlan.actual_quantity || 0 }}</el-descriptions-item>
          <el-descriptions-item label="计划日期">{{ currentPlan.plan_date }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentPlan.status)">
              {{ getStatusText(currentPlan.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentPlan.remark || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin-top: 20px;">物料清单</h3>
        <el-table :data="currentPlan.items" style="width: 100%">
          <el-table-column prop="material_code" label="原材料编码" />
          <el-table-column prop="material_name" label="原材料名称" />
          <el-table-column prop="required_quantity" label="需要数量" />
          <el-table-column prop="consumed_quantity" label="已消耗数量" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { productionPlanApi, type ProductionPlan, type ProductionPlanForm } from '@/api/productionPlans'
import { productApi, type Product } from '@/api/products'
import { materialApi, type Material } from '@/api/materials'
import { useWindowSize } from '@/composables/useWindowSize'

const { isMobile } = useWindowSize()

// 响应式数据
const productionPlans = ref<ProductionPlan[]>([])
const products = ref<Product[]>([])
const materials = ref<Material[]>([])
const loading = ref(false)
const submitLoading = ref(false)
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const currentPlan = ref<ProductionPlan | null>(null)
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表单数据
const planFormRef = ref<FormInstance>()
const planForm = reactive<ProductionPlanForm>({
  product_id: 0,
  planned_quantity: 1,
  plan_date: '',
  remark: '',
  materials: []
})

// 表单验证规则
const planRules: FormRules = {
  product_id: [{ required: true, message: '请选择成品', trigger: 'change' }],
  planned_quantity: [{ required: true, message: '请输入计划数量', trigger: 'blur' }],
  plan_date: [{ required: true, message: '请选择计划日期', trigger: 'change' }]
}

// 状态相关方法
function getStatusType(status: string) {
  const statusMap: Record<string, string> = {
    draft: 'info',
    approved: 'success',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

function getStatusText(status: string) {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    approved: '已审核',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取数据的方法
async function fetchProductionPlans() {
  loading.value = true
  try {
    const response = await productionPlanApi.getProductionPlans({
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      status: statusFilter.value
    })
    
    if (response.success && response.data) {
      productionPlans.value = response.data.data
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取生产计划列表失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取生产计划列表失败')
  } finally {
    loading.value = false
  }
}

async function fetchProducts() {
  try {
    const response = await productApi.getProducts({ pageSize: 1000 })
    products.value = response.products || []
  } catch (error) {
    console.error('获取成品列表失败:', error)
  }
}

async function fetchMaterials() {
  try {
    const response = await materialApi.getMaterials({ limit: 1000 })
    if (response.success && response.data) {
      materials.value = response.data.data
    }
  } catch (error) {
    console.error('获取原材料列表失败:', error)
  }
}

// 搜索和分页
function handleSearch() {
  currentPage.value = 1
  fetchProductionPlans()
}

function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  fetchProductionPlans()
}

function handleCurrentChange(page: number) {
  currentPage.value = page
  fetchProductionPlans()
}

// 计划操作
async function viewPlan(plan: ProductionPlan) {
  try {
    const response = await productionPlanApi.getProductionPlan(plan.id)
    if (response.success && response.data) {
      currentPlan.value = response.data
      showDetailDialog.value = true
    }
  } catch (error: any) {
    ElMessage.error(error.message || '获取计划详情失败')
  }
}

async function approvePlan(plan: ProductionPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要审核生产计划 "${plan.plan_no}" 吗？`,
      '确认审核',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    const response = await productionPlanApi.approveProductionPlan(plan.id)
    if (response.success) {
      ElMessage.success('审核成功')
      fetchProductionPlans()
    } else {
      ElMessage.error(response.message || '审核失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '审核失败')
    }
  }
}

async function startPlan(plan: ProductionPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要开始生产计划 "${plan.plan_no}" 吗？`,
      '确认开始',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    const response = await productionPlanApi.startProduction(plan.id)
    if (response.success) {
      ElMessage.success('生产开始成功')
      fetchProductionPlans()
    } else {
      ElMessage.error(response.message || '开始失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '开始失败')
    }
  }
}

async function deletePlan(plan: ProductionPlan) {
  try {
    await ElMessageBox.confirm(
      `确定要删除生产计划 "${plan.plan_no}" 吗？`,
      '确认删除',
      { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
    )
    
    const response = await productionPlanApi.deleteProductionPlan(plan.id)
    if (response.success) {
      ElMessage.success('删除成功')
      fetchProductionPlans()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

// 物料操作
function addMaterial() {
  planForm.materials.push({
    material_id: 0,
    required_quantity: 1
  })
}

function removeMaterial(index: number) {
  planForm.materials.splice(index, 1)
}

// 对话框操作
function cancelDialog() {
  showCreateDialog.value = false
  planFormRef.value?.resetFields()
  Object.assign(planForm, {
    product_id: 0,
    planned_quantity: 1,
    plan_date: '',
    remark: '',
    materials: []
  })
}

async function submitPlan() {
  if (!planFormRef.value) return
  
  const valid = await planFormRef.value.validate().catch(() => false)
  if (!valid) return
  
  if (planForm.materials.length === 0) {
    ElMessage.error('请添加物料清单')
    return
  }
  
  // 验证物料数据
  for (const material of planForm.materials) {
    if (!material.material_id || material.required_quantity <= 0) {
      ElMessage.error('请完善物料清单信息')
      return
    }
  }
  
  submitLoading.value = true
  
  try {
    const response = await productionPlanApi.createProductionPlan(planForm)
    
    if (response.success) {
      ElMessage.success('生产计划创建成功')
      showCreateDialog.value = false
      fetchProductionPlans()
      cancelDialog()
    } else {
      ElMessage.error(response.message || '保存失败')
    }
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    submitLoading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProductionPlans()
  fetchProducts()
  fetchMaterials()
})
</script>

<style scoped>
.production-plans-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-title {
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.operations-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.operations-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 200px;
}

.operations-right {
  flex-shrink: 0;
}

.table-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.materials-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h3 {
  margin: 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.mobile-table {
  font-size: 14px;
}

@media (max-width: 768px) {
  .production-plans-container {
    padding: 10px;
  }
  
  .operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .operations-left {
    margin-bottom: 10px;
    flex-direction: column;
    gap: 10px;
  }
  
  .table-container {
    padding: 10px;
    overflow-x: auto;
  }
  
  .pagination-container {
    padding: 10px;
  }
}
</style>
