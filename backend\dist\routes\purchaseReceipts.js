"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const purchaseReceiptController_1 = require("../controllers/purchaseReceiptController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/purchase-receipts - 获取采购入库单列表
router.get('/', purchaseReceiptController_1.getPurchaseReceipts);
// GET /api/purchase-receipts/:id - 获取单个采购入库单详情
router.get('/:id', purchaseReceiptController_1.getPurchaseReceipt);
// POST /api/purchase-receipts - 创建采购入库单
router.post('/', purchaseReceiptController_1.createPurchaseReceipt);
exports.default = router;
//# sourceMappingURL=purchaseReceipts.js.map