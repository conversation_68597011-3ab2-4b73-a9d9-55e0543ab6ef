<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <el-header class="header">
      <div class="header-left">
        <!-- 移动端菜单按钮 -->
        <el-button
          v-if="isMobile"
          class="mobile-menu-btn"
          @click="toggleSidebar"
          :icon="Menu"
          circle
        />
        <h1 class="header-title">{{ isMobile ? 'ERP系统' : 'ERP进销存管理系统' }}</h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            <span v-if="!isMobile">{{ authStore.user?.username }}</span>
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- 遮罩层 (移动端) -->
      <div
        v-if="isMobile && sidebarVisible"
        class="sidebar-overlay"
        @click="closeSidebar"
      />

      <!-- 侧边栏 -->
      <el-aside
        class="sidebar"
        :class="{
          'sidebar-mobile': isMobile,
          'sidebar-visible': sidebarVisible,
          'sidebar-collapsed': isTablet && !sidebarVisible
        }"
        :width="sidebarWidth"
      >
        <el-menu
          :default-active="activeMenu"
          class="sidebar-menu"
          :collapse="isTablet && !sidebarVisible"
          router
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>

          <el-sub-menu index="basic-data">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>基础数据</span>
            </template>
            <el-menu-item index="/materials">
              <el-icon><Box /></el-icon>
              <span>原材料管理</span>
            </el-menu-item>
            <el-menu-item index="/products">
              <el-icon><Goods /></el-icon>
              <span>成品管理</span>
            </el-menu-item>
            <el-menu-item index="/suppliers">
              <el-icon><UserFilled /></el-icon>
              <span>供应商管理</span>
            </el-menu-item>
            <el-menu-item index="/customers">
              <el-icon><Avatar /></el-icon>
              <span>客户管理</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="purchase">
            <template #title>
              <el-icon><ShoppingBag /></el-icon>
              <span>采购管理</span>
            </template>
            <el-menu-item index="/purchase-orders">
              <el-icon><Document /></el-icon>
              <span>采购订单</span>
            </el-menu-item>
            <el-menu-item index="/purchase-receipts">
              <el-icon><Van /></el-icon>
              <span>采购入库</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="sales">
            <template #title>
              <el-icon><Goods /></el-icon>
              <span>销售管理</span>
            </template>
            <el-menu-item index="/sales-orders">
              <el-icon><Document /></el-icon>
              <span>销售订单</span>
            </el-menu-item>
            <el-menu-item index="/sales-deliveries">
              <el-icon><Van /></el-icon>
              <span>销售出库</span>
            </el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="production">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>生产管理</span>
            </template>
            <el-menu-item index="/production-plans">
              <el-icon><Document /></el-icon>
              <span>生产计划</span>
            </el-menu-item>
            <el-menu-item index="/production-completions">
              <el-icon><Box /></el-icon>
              <span>生产完工</span>
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="content" :class="{ 'content-mobile': isMobile }">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowDown, House, Document, Box, Goods, Menu, UserFilled, Avatar, ShoppingBag, Van, Setting } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useWindowSize } from '@/composables/useWindowSize'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 使用全局窗口大小管理器
const { isMobile, isTablet, sidebarWidth } = useWindowSize()
const sidebarVisible = ref(false)

// 当前激活的菜单项
const activeMenu = computed(() => route.path)

// 监听屏幕大小变化，自动调整侧边栏状态
watch([isMobile, isTablet], ([mobile, tablet]) => {
  if (mobile) {
    sidebarVisible.value = false
  } else if (!mobile && !tablet) {
    // 桌面端自动显示侧边栏
    sidebarVisible.value = true
  }
}, { immediate: true })

// 切换侧边栏显示
function toggleSidebar() {
  sidebarVisible.value = !sidebarVisible.value
}

// 关闭侧边栏
function closeSidebar() {
  sidebarVisible.value = false
}

// 处理下拉菜单命令
async function handleCommand(command: string) {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm(
        '确定要退出登录吗？',
        '确认退出',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
      
      authStore.logout()
      ElMessage.success('已退出登录')
      router.push('/login')
    } catch (error) {
      // 用户取消操作
    }
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1001;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mobile-menu-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
}

.mobile-menu-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.header-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-info .el-icon {
  margin: 0 4px;
}

.main-container {
  flex: 1;
  overflow: hidden;
  width: 100%;
  position: relative;
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 侧边栏样式 */
.sidebar {
  background-color: #f5f5f5;
  border-right: 1px solid #e6e6e6;
  transition: all 0.3s ease;
  z-index: 1000;
}

/* 移动端侧边栏 */
.sidebar-mobile {
  position: fixed;
  top: 60px;
  left: 0;
  bottom: 0;
  transform: translateX(-100%);
}

.sidebar-mobile.sidebar-visible {
  transform: translateX(0);
}

/* 平板端折叠侧边栏 */
.sidebar-collapsed {
  width: 64px !important;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.content {
  background-color: #f8f9fa;
  overflow-y: auto;
  transition: margin-left 0.3s ease;
}

.content-mobile {
  margin-left: 0;
}

/* 响应式断点 */
@media (max-width: 767px) {
  .header {
    padding: 0 12px;
  }

  .header-title {
    font-size: 16px;
  }

  .content {
    padding: 16px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .header-title {
    font-size: 18px;
  }
}

@media (min-width: 1024px) {
  .mobile-menu-btn {
    display: none;
  }

  .sidebar-overlay {
    display: none;
  }

  .sidebar-mobile {
    position: static;
    transform: none;
  }
}
</style>
