"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const salesOrderController_1 = require("../controllers/salesOrderController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/sales-orders - 获取销售订单列表
router.get('/', salesOrderController_1.getSalesOrders);
// GET /api/sales-orders/:id - 获取单个销售订单详情
router.get('/:id', salesOrderController_1.getSalesOrder);
// POST /api/sales-orders - 创建销售订单
router.post('/', salesOrderController_1.createSalesOrder);
// POST /api/sales-orders/:id/approve - 审核销售订单
router.post('/:id/approve', salesOrderController_1.approveSalesOrder);
// PUT /api/sales-orders/:id/status - 更新销售订单状态
router.put('/:id/status', salesOrderController_1.updateSalesOrderStatus);
// DELETE /api/sales-orders/:id - 删除销售订单
router.delete('/:id', salesOrderController_1.deleteSalesOrder);
exports.default = router;
//# sourceMappingURL=salesOrders.js.map