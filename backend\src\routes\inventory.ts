import express from 'express';
import { 
  getInventoryList,
  getMaterialsInventory,
  getProductsInventory,
  getInventoryHistory,
  getInventoryAlerts
} from '../controllers/inventoryController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/inventory - 获取库存列表（原材料和成品）
router.get('/', getInventoryList);

// GET /api/inventory/materials - 获取原材料库存列表
router.get('/materials', getMaterialsInventory);

// GET /api/inventory/products - 获取成品库存列表
router.get('/products', getProductsInventory);

// GET /api/inventory/:itemType/:itemId/history - 获取库存变动历史
router.get('/:itemType/:itemId/history', getInventoryHistory);

// GET /api/inventory/alerts - 获取库存预警信息
router.get('/alerts', getInventoryAlerts);

export default router;
