# CLAUDE.md

始终用中文回复

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a lightweight ERP inventory management system (ERP进销存管理系统) designed for small companies (30 employees). It's a full-stack application with Vue 3 frontend and Node.js backend using SQLite database.

## Development Commands

### Backend (Node.js + Express + TypeScript)
```bash
cd backend
npm install          # Install dependencies
npm run dev          # Start development server with nodemon
npm run build        # Compile TypeScript to JavaScript
npm start            # Run compiled JavaScript from dist/
```

### Frontend (Vue 3 + TypeScript + Vite)
```bash
cd frontend
npm install          # Install dependencies
npm run dev          # Start Vite development server
npm run build        # Build for production (requires type-check)
npm run type-check   # Run TypeScript type checking
npm run lint         # Run ESLint with auto-fix
npm run format       # Format code with Prettier
npm run preview      # Preview production build
```

## Architecture

### Tech Stack
- **Frontend**: Vue 3, TypeScript, Element Plus, Pinia, Vue Router, Vite
- **Backend**: Node.js, Express, TypeScript, SQLite, JWT authentication, bcryptjs
- **Database**: SQLite with comprehensive schema for ERP functionality

### Project Structure
```
├── frontend/           # Vue 3 application
│   ├── src/
│   │   ├── api/           # API clients for all modules
│   │   ├── components/    # Reusable components (DataTable, FormDialog, LoadingState, EmptyState)
│   │   ├── views/         # Page components for all modules
│   │   ├── stores/        # Pinia state management (auth.ts)
│   │   ├── router/        # Vue Router with auth guards
│   │   └── composables/   # Vue composition functions
├── backend/            # Express API server
│   ├── src/
│   │   ├── controllers/   # Route handlers for all modules
│   │   ├── models/        # Database initialization and schema
│   │   ├── routes/        # API route definitions for all modules
│   │   ├── middleware/    # JWT authentication middleware
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Utility functions (empty)
│   └── data/             # SQLite database file (erp.db)
├── docs/               # Project documentation and PRDs
```

### Complete Module Structure
The system includes controllers, routes, views, and APIs for:
- **Basic Data**: Materials, Products, Suppliers, Customers
- **Purchase Management**: Purchase Orders, Purchase Receipts
- **Sales Management**: Sales Orders, Sales Deliveries  
- **Production Management**: Production Plans, Production Completions
- **Authentication**: User management with JWT

### Database Schema
Complete SQLite schema with 15+ tables including:
- Core entities: `users`, `materials`, `products`, `suppliers`, `customers`
- Purchase flow: `purchase_orders`, `purchase_order_items`, `purchase_receipts`, `purchase_receipt_items`
- Sales flow: `sales_orders`, `sales_order_items`, `sales_deliveries`, `sales_delivery_items` 
- Production flow: `production_plans`, `production_plan_items`, `production_completions`, `production_completion_items`

All tables include proper foreign key relationships and standard audit fields (created_at, updated_at).

## Development Status

### ✅ Completed Infrastructure
- Complete database schema with all ERP modules
- Full controller and route structure for all modules
- Vue router with all module routes configured
- API client structure for all modules
- User authentication system with JWT and bcryptjs
- Default admin user creation (admin/123456)

### 🚧 Implementation Status
- **Materials Management**: Fully implemented CRUD operations
- **Other Modules**: Controllers and routes exist but may need view implementations

## Key Development Patterns

### Frontend
- Uses Element Plus for UI components
- Pinia stores for state management (currently only auth implemented)
- Composition API pattern in Vue components
- TypeScript interfaces for type safety
- Axios for HTTP requests with interceptors
- Lazy-loaded routes for better performance

### Backend
- Express with TypeScript
- SQLite with direct SQL queries (no ORM)
- JWT-based authentication middleware
- Consistent error handling and response formatting
- Automatic database initialization with schema creation
- Default user creation on first run

### Database Patterns
- Auto-incrementing integer primary keys
- UTF-8 encoding configured
- Consistent naming conventions (snake_case)
- Standard audit fields on all tables
- Proper foreign key constraints

## Default Credentials
- Username: admin
- Password: 123456
- Role: admin

## Database Location
SQLite database file is located at `backend/data/erp.db` and is automatically created on first run.

## Testing
Currently no test framework is configured. The package.json test script returns an error message.