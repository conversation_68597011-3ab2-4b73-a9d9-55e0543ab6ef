import { Request, Response } from 'express';
export declare function getProductionPlans(req: Request, res: Response): Promise<void>;
export declare function getProductionPlan(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createProductionPlan(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function approveProductionPlan(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function startProduction(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updateProductionPlanStatus(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function deleteProductionPlan(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=productionPlanController.d.ts.map