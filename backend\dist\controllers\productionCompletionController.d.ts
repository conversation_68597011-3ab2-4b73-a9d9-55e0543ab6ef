import { Request, Response } from 'express';
export declare function getProductionCompletions(req: Request, res: Response): Promise<void>;
export declare function getProductionCompletion(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createProductionCompletion(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=productionCompletionController.d.ts.map