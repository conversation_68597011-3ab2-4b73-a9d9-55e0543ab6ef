import express from 'express';
import { 
  getProductionCompletions, 
  createProductionCompletion, 
  getProductionCompletion
} from '../controllers/productionCompletionController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/production-completions - 获取生产完工单列表
router.get('/', getProductionCompletions);

// GET /api/production-completions/:id - 获取单个生产完工单详情
router.get('/:id', getProductionCompletion);

// POST /api/production-completions - 创建生产完工单
router.post('/', createProductionCompletion);

export default router;
