import { apiClient } from './index'

// 库存盘点相关类型定义
export interface StocktakingTask {
  id: number
  task_no: string
  title: string
  description?: string
  task_date: string
  status: 'draft' | 'in_progress' | 'completed' | 'cancelled'
  created_by: number
  created_at: string
  updated_at: string
  completed_at?: string
  created_by_name?: string
}

export interface StocktakingItem {
  id: number
  stocktaking_task_id: number
  item_type: 'material' | 'product'
  item_id: number
  system_quantity: number
  actual_quantity?: number
  difference_quantity?: number
  status: 'pending' | 'counted' | 'adjusted'
  remark?: string
  counted_at?: string
  counted_by?: number
  adjusted_at?: string
  adjusted_by?: number
  created_at: string
  updated_at: string
  item_code?: string
  item_name?: string
  item_unit?: string
  counted_by_name?: string
  adjusted_by_name?: string
}

export interface StocktakingTaskCreateInput {
  title: string
  description?: string
  task_date: string
  item_types?: ('material' | 'product')[]
}

export interface StocktakingTaskUpdateInput {
  title?: string
  description?: string
  task_date?: string
  status?: 'draft' | 'in_progress' | 'completed' | 'cancelled'
}

export interface StocktakingItemUpdateInput {
  actual_quantity?: number
  status?: 'pending' | 'counted' | 'adjusted'
  remark?: string
}

export interface StocktakingTaskDetail {
  task: StocktakingTask
  items: StocktakingItem[]
}

export interface StocktakingQuery {
  page?: number
  limit?: number
  search?: string
  status?: 'draft' | 'in_progress' | 'completed' | 'cancelled'
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// API 函数
export const stocktakingApi = {
  // 获取盘点任务列表
  getStocktakingTasks: (params?: StocktakingQuery) => 
    apiClient.get<{ data: PaginatedResponse<StocktakingTask> }>('/stocktaking', { params }),

  // 获取单个盘点任务详情
  getStocktakingTask: (id: number) => 
    apiClient.get<{ data: StocktakingTaskDetail }>(`/stocktaking/${id}`),

  // 创建盘点任务
  createStocktakingTask: (data: StocktakingTaskCreateInput) => 
    apiClient.post<{ data: { task_no: string } }>('/stocktaking', data),

  // 更新盘点任务
  updateStocktakingTask: (id: number, data: StocktakingTaskUpdateInput) => 
    apiClient.put(`/stocktaking/${id}`, data),

  // 更新盘点明细
  updateStocktakingItem: (id: number, data: StocktakingItemUpdateInput) => 
    apiClient.put(`/stocktaking/items/${id}`, data),

  // 库存调整
  adjustInventory: (taskId: number, itemIds?: number[]) => 
    apiClient.post(`/stocktaking/${taskId}/adjust`, { itemIds })
}

export default stocktakingApi
