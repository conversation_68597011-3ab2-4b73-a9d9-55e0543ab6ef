import { Request, Response } from 'express';
export declare function getSalesDeliveries(req: Request, res: Response): Promise<void>;
export declare function getSalesDelivery(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createSalesDelivery(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=salesDeliveryController.d.ts.map