"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProductionPlans = getProductionPlans;
exports.getProductionPlan = getProductionPlan;
exports.createProductionPlan = createProductionPlan;
exports.approveProductionPlan = approveProductionPlan;
exports.startProduction = startProduction;
exports.updateProductionPlanStatus = updateProductionPlanStatus;
exports.deleteProductionPlan = deleteProductionPlan;
const database_1 = require("../models/database");
// 生成生产计划号
function generatePlanNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getTime()).slice(-6); // 取时间戳后6位
    return `PP${year}${month}${day}${time}`;
}
// 获取生产计划列表
async function getProductionPlans(req, res) {
    try {
        const { page = 1, limit = 10, search = '', status = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE 1=1";
        const params = [];
        if (search) {
            whereClause += " AND (pp.plan_no LIKE ? OR p.name LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern);
        }
        if (status) {
            whereClause += " AND pp.status = ?";
            params.push(status);
        }
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total 
      FROM production_plans pp
      LEFT JOIN products p ON pp.product_id = p.id
      ${whereClause}
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        pp.*,
        p.code as product_code,
        p.name as product_name
      FROM production_plans pp
      LEFT JOIN products p ON pp.product_id = p.id
      ${whereClause}
      ORDER BY pp.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const plans = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: plans,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取生产计划列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取生产计划列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个生产计划详情（包含物料明细）
async function getProductionPlan(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 获取计划主信息
        const plan = await new Promise((resolve, reject) => {
            db.get(`
        SELECT 
          pp.*,
          p.code as product_code,
          p.name as product_name
        FROM production_plans pp
        LEFT JOIN products p ON pp.product_id = p.id
        WHERE pp.id = ?
      `, [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '生产计划不存在'
            });
        }
        // 获取物料明细
        const items = await new Promise((resolve, reject) => {
            db.all(`
        SELECT 
          ppi.*,
          m.code as material_code,
          m.name as material_name,
          m.unit
        FROM production_plan_items ppi
        LEFT JOIN materials m ON ppi.material_id = m.id
        WHERE ppi.production_plan_id = ?
        ORDER BY ppi.id
      `, [id], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const planWithItems = {
            ...plan,
            items
        };
        res.json({
            success: true,
            message: '获取生产计划成功',
            data: planWithItems
        });
    }
    catch (error) {
        console.error('获取生产计划错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建生产计划
async function createProductionPlan(req, res) {
    try {
        const { product_id, planned_quantity, plan_date, remark = '', materials } = req.body;
        // 验证必填字段
        if (!product_id || !planned_quantity || !plan_date || !materials || materials.length === 0) {
            return res.status(400).json({
                success: false,
                message: '成品、计划数量、计划日期和物料清单不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        const planNo = generatePlanNo();
        // 开始事务
        await new Promise((resolve, reject) => {
            db.serialize(async () => {
                try {
                    db.run('BEGIN TRANSACTION');
                    // 创建生产计划主表
                    const planId = await new Promise((resolve, reject) => {
                        db.run(`
              INSERT INTO production_plans (
                plan_no, product_id, planned_quantity, plan_date, remark
              ) VALUES (?, ?, ?, ?, ?)
            `, [planNo, product_id, planned_quantity, plan_date, remark], function (err) {
                            if (err)
                                reject(err);
                            else
                                resolve(this.lastID);
                        });
                    });
                    // 创建物料明细
                    for (const material of materials) {
                        await new Promise((resolve, reject) => {
                            db.run(`
                INSERT INTO production_plan_items (
                  production_plan_id, material_id, required_quantity
                ) VALUES (?, ?, ?)
              `, [planId, material.material_id, material.required_quantity], (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        });
                    }
                    db.run('COMMIT', (err) => {
                        if (err)
                            reject(err);
                        else
                            resolve();
                    });
                }
                catch (error) {
                    db.run('ROLLBACK');
                    reject(error);
                }
            });
        });
        res.status(201).json({
            success: true,
            message: '生产计划创建成功',
            data: { plan_no: planNo }
        });
    }
    catch (error) {
        console.error('创建生产计划错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 审核生产计划
async function approveProductionPlan(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查计划是否存在且状态为草稿
        const plan = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM production_plans WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '生产计划不存在'
            });
        }
        if (plan.status !== 'draft') {
            return res.status(400).json({
                success: false,
                message: '只有草稿状态的计划才能审核'
            });
        }
        // 更新计划状态为已审核
        await new Promise((resolve, reject) => {
            db.run('UPDATE production_plans SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['approved', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '生产计划审核成功'
        });
    }
    catch (error) {
        console.error('审核生产计划错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 开始生产
async function startProduction(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查计划是否存在且状态为已审核
        const plan = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM production_plans WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '生产计划不存在'
            });
        }
        if (plan.status !== 'approved') {
            return res.status(400).json({
                success: false,
                message: '只有已审核的计划才能开始生产'
            });
        }
        // 更新计划状态为进行中，并设置开始日期
        await new Promise((resolve, reject) => {
            db.run('UPDATE production_plans SET status = ?, start_date = CURRENT_DATE, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['in_progress', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '生产开始成功'
        });
    }
    catch (error) {
        console.error('开始生产错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 更新生产计划状态
async function updateProductionPlanStatus(req, res) {
    try {
        const { id } = req.params;
        const { status } = req.body;
        if (!status) {
            return res.status(400).json({
                success: false,
                message: '状态不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        await new Promise((resolve, reject) => {
            db.run('UPDATE production_plans SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [status, id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '生产计划状态更新成功'
        });
    }
    catch (error) {
        console.error('更新生产计划状态错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 删除生产计划
async function deleteProductionPlan(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查计划是否存在
        const plan = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM production_plans WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '生产计划不存在'
            });
        }
        if (plan.status === 'completed') {
            return res.status(400).json({
                success: false,
                message: '已完成的计划不能删除'
            });
        }
        // 开始事务删除计划和明细
        await new Promise((resolve, reject) => {
            db.serialize(() => {
                db.run('BEGIN TRANSACTION');
                // 删除物料明细
                db.run('DELETE FROM production_plan_items WHERE production_plan_id = ?', [id], (err) => {
                    if (err) {
                        db.run('ROLLBACK');
                        reject(err);
                        return;
                    }
                    // 删除计划主表
                    db.run('DELETE FROM production_plans WHERE id = ?', [id], (err) => {
                        if (err) {
                            db.run('ROLLBACK');
                            reject(err);
                        }
                        else {
                            db.run('COMMIT', (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        }
                    });
                });
            });
        });
        res.json({
            success: true,
            message: '生产计划删除成功'
        });
    }
    catch (error) {
        console.error('删除生产计划错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=productionPlanController.js.map