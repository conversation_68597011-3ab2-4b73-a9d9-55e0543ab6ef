import { Request, Response } from 'express';
export declare function getCustomers(req: Request, res: Response): Promise<void>;
export declare function getCustomer(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createCustomer(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function updateCustomer(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function deleteCustomer(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=customerController.d.ts.map