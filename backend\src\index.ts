import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { initDatabase } from './models/database';
import authRoutes from './routes/auth';
import materialRoutes from './routes/materials';
import productRoutes from './routes/products';
import supplierRoutes from './routes/suppliers';
import customerRoutes from './routes/customers';
import purchaseOrderRoutes from './routes/purchaseOrders';
import purchaseReceiptRoutes from './routes/purchaseReceipts';
import salesOrderRoutes from './routes/salesOrders';
import salesDeliveryRoutes from './routes/salesDeliveries';
import productionPlanRoutes from './routes/productionPlans';
import productionCompletionRoutes from './routes/productionCompletions';

// 加载环境变量
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 基础路由
app.get('/', (req, res) => {
  res.json({ message: 'ERP进销存管理系统 API 服务器运行中' });
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/materials', materialRoutes);
app.use('/api/products', productRoutes);
app.use('/api/suppliers', supplierRoutes);
app.use('/api/customers', customerRoutes);
app.use('/api/purchase-orders', purchaseOrderRoutes);
app.use('/api/purchase-receipts', purchaseReceiptRoutes);
app.use('/api/sales-orders', salesOrderRoutes);
app.use('/api/sales-deliveries', salesDeliveryRoutes);
app.use('/api/production-plans', productionPlanRoutes);
app.use('/api/production-completions', productionCompletionRoutes);

// 启动服务器
async function startServer() {
  try {
    // 初始化数据库
    await initDatabase();
    console.log('数据库初始化成功');

    app.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log(`API地址: http://localhost:${PORT}`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

startServer();
