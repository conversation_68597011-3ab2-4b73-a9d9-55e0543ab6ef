"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const purchaseOrderController_1 = require("../controllers/purchaseOrderController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/purchase-orders - 获取采购订单列表
router.get('/', purchaseOrderController_1.getPurchaseOrders);
// GET /api/purchase-orders/:id - 获取单个采购订单详情
router.get('/:id', purchaseOrderController_1.getPurchaseOrder);
// POST /api/purchase-orders - 创建采购订单
router.post('/', purchaseOrderController_1.createPurchaseOrder);
// POST /api/purchase-orders/:id/approve - 审核采购订单
router.post('/:id/approve', purchaseOrderController_1.approvePurchaseOrder);
// PUT /api/purchase-orders/:id/status - 更新采购订单状态
router.put('/:id/status', purchaseOrderController_1.updatePurchaseOrderStatus);
// DELETE /api/purchase-orders/:id - 删除采购订单
router.delete('/:id', purchaseOrderController_1.deletePurchaseOrder);
exports.default = router;
//# sourceMappingURL=purchaseOrders.js.map