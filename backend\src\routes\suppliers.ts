import express from 'express';
import { 
  getSuppliers, 
  createSupplier, 
  updateSupplier, 
  deleteSupplier, 
  getSupplier 
} from '../controllers/supplierController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/suppliers - 获取供应商列表
router.get('/', getSuppliers);

// GET /api/suppliers/:id - 获取单个供应商详情
router.get('/:id', getSupplier);

// POST /api/suppliers - 创建供应商
router.post('/', createSupplier);

// PUT /api/suppliers/:id - 更新供应商
router.put('/:id', updateSupplier);

// DELETE /api/suppliers/:id - 删除供应商
router.delete('/:id', deleteSupplier);

export default router;
