import { Request, Response } from 'express';
export declare function getPurchaseReceipts(req: Request, res: Response): Promise<void>;
export declare function getPurchaseReceipt(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
export declare function createPurchaseReceipt(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=purchaseReceiptController.d.ts.map