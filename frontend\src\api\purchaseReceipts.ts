import api from './index'
import type { ApiResponse } from './auth'

// 采购入库单相关类型定义
export interface PurchaseReceipt {
  id: number
  receipt_no: string
  purchase_order_id: number
  purchase_order_no?: string
  supplier_id: number
  supplier_name?: string
  receipt_date: string
  total_amount: number
  status: 'draft' | 'confirmed'
  remark?: string
  created_at: string
  updated_at: string
  items?: PurchaseReceiptItem[]
}

export interface PurchaseReceiptItem {
  id: number
  purchase_receipt_id: number
  material_id: number
  material_code?: string
  material_name?: string
  unit?: string
  quantity: number
  unit_price: number
  total_price: number
  created_at: string
  updated_at: string
}

export interface PurchaseReceiptForm {
  purchase_order_id: number
  receipt_date: string
  remark?: string
  items: {
    material_id: number
    quantity: number
    unit_price: number
  }[]
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface PurchaseReceiptQuery {
  page?: number
  limit?: number
  search?: string
  status?: string
}

// 采购入库单API
export const purchaseReceiptApi = {
  // 获取采购入库单列表
  getPurchaseReceipts(params?: PurchaseReceiptQuery): Promise<ApiResponse<PaginatedResponse<PurchaseReceipt>>> {
    return api.get('/purchase-receipts', { params })
  },

  // 获取单个采购入库单
  getPurchaseReceipt(id: number): Promise<ApiResponse<PurchaseReceipt>> {
    return api.get(`/purchase-receipts/${id}`)
  },

  // 创建采购入库单
  createPurchaseReceipt(data: PurchaseReceiptForm): Promise<ApiResponse> {
    return api.post('/purchase-receipts', data)
  }
}
