export interface User {
    id: number;
    username: string;
    password: string;
    email?: string;
    role: 'admin' | 'user';
    created_at: string;
    updated_at: string;
}
export interface UserCreateInput {
    username: string;
    password: string;
    email?: string;
    role?: 'admin' | 'user';
}
export interface UserLoginInput {
    username: string;
    password: string;
}
export interface Material {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface MaterialCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface MaterialUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface Product {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    sale_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface ProductCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface ProductUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface Supplier {
    id: number;
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface SupplierCreateInput {
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
}
export interface SupplierUpdateInput {
    code?: string;
    name?: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
    status?: 'active' | 'inactive';
}
export interface Customer {
    id: number;
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface CustomerCreateInput {
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
}
export interface CustomerUpdateInput {
    code?: string;
    name?: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
    status?: 'active' | 'inactive';
}
export interface PurchaseOrder {
    id: number;
    order_no: string;
    supplier_id: number;
    supplier_name?: string;
    order_date: string;
    expected_date?: string;
    total_amount: number;
    status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface PurchaseOrderItem {
    id: number;
    purchase_order_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    received_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface PurchaseOrderCreateInput {
    supplier_id: number;
    order_date: string;
    expected_date?: string;
    remark?: string;
    items: {
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface PurchaseOrderUpdateInput {
    supplier_id?: number;
    order_date?: string;
    expected_date?: string;
    remark?: string;
    status?: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    items?: {
        id?: number;
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface PurchaseReceipt {
    id: number;
    receipt_no: string;
    purchase_order_id: number;
    purchase_order_no?: string;
    supplier_id: number;
    supplier_name?: string;
    receipt_date: string;
    total_amount: number;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface PurchaseReceiptItem {
    id: number;
    purchase_receipt_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    created_at: string;
    updated_at: string;
}
export interface PurchaseReceiptCreateInput {
    purchase_order_id: number;
    receipt_date: string;
    remark?: string;
    items: {
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesOrder {
    id: number;
    order_no: string;
    customer_id: number;
    customer_name?: string;
    order_date: string;
    delivery_date?: string;
    total_amount: number;
    status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface SalesOrderItem {
    id: number;
    sales_order_id: number;
    product_id: number;
    product_code?: string;
    product_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    delivered_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface SalesOrderCreateInput {
    customer_id: number;
    order_date: string;
    delivery_date?: string;
    remark?: string;
    items: {
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesOrderUpdateInput {
    customer_id?: number;
    order_date?: string;
    delivery_date?: string;
    remark?: string;
    status?: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    items?: {
        id?: number;
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesDelivery {
    id: number;
    delivery_no: string;
    sales_order_id: number;
    sales_order_no?: string;
    customer_id: number;
    customer_name?: string;
    delivery_date: string;
    total_amount: number;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface SalesDeliveryItem {
    id: number;
    sales_delivery_id: number;
    product_id: number;
    product_code?: string;
    product_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    created_at: string;
    updated_at: string;
}
export interface SalesDeliveryCreateInput {
    sales_order_id: number;
    delivery_date: string;
    remark?: string;
    items: {
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface ProductionPlan {
    id: number;
    plan_no: string;
    product_id: number;
    product_code?: string;
    product_name?: string;
    planned_quantity: number;
    actual_quantity?: number;
    plan_date: string;
    start_date?: string;
    completion_date?: string;
    status: 'draft' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface ProductionPlanItem {
    id: number;
    production_plan_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    required_quantity: number;
    consumed_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface ProductionPlanCreateInput {
    product_id: number;
    planned_quantity: number;
    plan_date: string;
    remark?: string;
    materials: {
        material_id: number;
        required_quantity: number;
    }[];
}
export interface ProductionPlanUpdateInput {
    product_id?: number;
    planned_quantity?: number;
    plan_date?: string;
    start_date?: string;
    completion_date?: string;
    status?: 'draft' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
    remark?: string;
}
export interface ProductionCompletion {
    id: number;
    completion_no: string;
    production_plan_id: number;
    production_plan_no?: string;
    product_id: number;
    product_code?: string;
    product_name?: string;
    completed_quantity: number;
    completion_date: string;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface ProductionCompletionItem {
    id: number;
    production_completion_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    consumed_quantity: number;
    created_at: string;
    updated_at: string;
}
export interface ProductionCompletionCreateInput {
    production_plan_id: number;
    completed_quantity: number;
    completion_date: string;
    remark?: string;
    materials: {
        material_id: number;
        consumed_quantity: number;
    }[];
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    search?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
//# sourceMappingURL=index.d.ts.map