import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import LayoutView from '../views/LayoutView.vue'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import MaterialsView from '../views/MaterialsView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      component: LayoutView,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'home',
          component: HomeView
        },
        {
          path: 'materials',
          name: 'materials',
          component: MaterialsView
        },
        {
          path: 'products',
          name: 'products',
          component: () => import('../views/ProductsView.vue')
        },
        {
          path: 'suppliers',
          name: 'suppliers',
          component: () => import('../views/SuppliersView.vue')
        },
        {
          path: 'customers',
          name: 'customers',
          component: () => import('../views/CustomersView.vue')
        },
        {
          path: 'purchase-orders',
          name: 'purchase-orders',
          component: () => import('../views/PurchaseOrdersView.vue')
        },
        {
          path: 'purchase-receipts',
          name: 'purchase-receipts',
          component: () => import('../views/PurchaseReceiptsView.vue')
        },
        {
          path: 'sales-orders',
          name: 'sales-orders',
          component: () => import('../views/SalesOrdersView.vue')
        },
        {
          path: 'sales-deliveries',
          name: 'sales-deliveries',
          component: () => import('../views/SalesDeliveriesView.vue')
        },
        {
          path: 'production-plans',
          name: 'production-plans',
          component: () => import('../views/ProductionPlansView.vue')
        },
        {
          path: 'production-completions',
          name: 'production-completions',
          component: () => import('../views/ProductionCompletionsView.vue')
        }
      ]
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initAuth()

  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)

  if (requiresAuth && !authStore.isLoggedIn) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
  } else if (requiresGuest && authStore.isLoggedIn) {
    // 需要游客状态但已登录，跳转到首页
    next('/')
  } else {
    next()
  }
})

export default router
