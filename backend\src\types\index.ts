// 用户相关类型
export interface User {
  id: number;
  username: string;
  password: string;
  email?: string;
  role: 'admin' | 'user';
  created_at: string;
  updated_at: string;
}

export interface UserCreateInput {
  username: string;
  password: string;
  email?: string;
  role?: 'admin' | 'user';
}

export interface UserLoginInput {
  username: string;
  password: string;
}

// 原材料相关类型
export interface Material {
  id: number;
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price: number;
  stock_min: number;
  stock_max: number;
  current_stock: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface MaterialCreateInput {
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
}

export interface MaterialUpdateInput {
  code?: string;
  name?: string;
  specification?: string;
  unit?: string;
  cost_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
  status?: 'active' | 'inactive';
}

// 成品相关类型
export interface Product {
  id: number;
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price: number;
  sale_price: number;
  stock_min: number;
  stock_max: number;
  current_stock: number;
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface ProductCreateInput {
  code: string;
  name: string;
  specification?: string;
  unit: string;
  cost_price?: number;
  sale_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
}

export interface ProductUpdateInput {
  code?: string;
  name?: string;
  specification?: string;
  unit?: string;
  cost_price?: number;
  sale_price?: number;
  stock_min?: number;
  stock_max?: number;
  current_stock?: number;
  status?: 'active' | 'inactive';
}

// 供应商相关类型
export interface Supplier {
  id: number;
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  settlement_method?: string; // 结算方式：现金、月结、季结等
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface SupplierCreateInput {
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  settlement_method?: string;
}

export interface SupplierUpdateInput {
  code?: string;
  name?: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  settlement_method?: string;
  status?: 'active' | 'inactive';
}

// 客户相关类型
export interface Customer {
  id: number;
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit?: number; // 信用额度
  status: 'active' | 'inactive';
  created_at: string;
  updated_at: string;
}

export interface CustomerCreateInput {
  code: string;
  name: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit?: number;
}

export interface CustomerUpdateInput {
  code?: string;
  name?: string;
  contact_person?: string;
  phone?: string;
  address?: string;
  credit_limit?: number;
  status?: 'active' | 'inactive';
}

// 采购订单相关类型
export interface PurchaseOrder {
  id: number;
  order_no: string; // 采购订单号
  supplier_id: number;
  supplier_name?: string; // 冗余字段，便于显示
  order_date: string;
  expected_date?: string; // 预期到货日期
  total_amount: number;
  status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled'; // 草稿、待审核、已审核、已完成、已取消
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrderItem {
  id: number;
  purchase_order_id: number;
  material_id: number;
  material_code?: string; // 冗余字段
  material_name?: string; // 冗余字段
  quantity: number;
  unit_price: number;
  total_price: number;
  received_quantity?: number; // 已收货数量
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrderCreateInput {
  supplier_id: number;
  order_date: string;
  expected_date?: string;
  remark?: string;
  items: {
    material_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface PurchaseOrderUpdateInput {
  supplier_id?: number;
  order_date?: string;
  expected_date?: string;
  remark?: string;
  status?: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
  items?: {
    id?: number; // 如果有id则更新，否则新增
    material_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

// 采购入库单相关类型
export interface PurchaseReceipt {
  id: number;
  receipt_no: string; // 入库单号
  purchase_order_id: number;
  purchase_order_no?: string; // 冗余字段
  supplier_id: number;
  supplier_name?: string; // 冗余字段
  receipt_date: string;
  total_amount: number;
  status: 'draft' | 'confirmed'; // 草稿、已确认
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface PurchaseReceiptItem {
  id: number;
  purchase_receipt_id: number;
  material_id: number;
  material_code?: string; // 冗余字段
  material_name?: string; // 冗余字段
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  updated_at: string;
}

export interface PurchaseReceiptCreateInput {
  purchase_order_id: number;
  receipt_date: string;
  remark?: string;
  items: {
    material_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

// 销售订单相关类型
export interface SalesOrder {
  id: number;
  order_no: string; // 销售订单号
  customer_id: number;
  customer_name?: string; // 冗余字段，便于显示
  order_date: string;
  delivery_date?: string; // 预期交货日期
  total_amount: number;
  status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled'; // 草稿、待审核、已审核、已完成、已取消
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface SalesOrderItem {
  id: number;
  sales_order_id: number;
  product_id: number;
  product_code?: string; // 冗余字段
  product_name?: string; // 冗余字段
  quantity: number;
  unit_price: number;
  total_price: number;
  delivered_quantity?: number; // 已发货数量
  created_at: string;
  updated_at: string;
}

export interface SalesOrderCreateInput {
  customer_id: number;
  order_date: string;
  delivery_date?: string;
  remark?: string;
  items: {
    product_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

export interface SalesOrderUpdateInput {
  customer_id?: number;
  order_date?: string;
  delivery_date?: string;
  remark?: string;
  status?: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
  items?: {
    id?: number; // 如果有id则更新，否则新增
    product_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

// 销售出库单相关类型
export interface SalesDelivery {
  id: number;
  delivery_no: string; // 出库单号
  sales_order_id: number;
  sales_order_no?: string; // 冗余字段
  customer_id: number;
  customer_name?: string; // 冗余字段
  delivery_date: string;
  total_amount: number;
  status: 'draft' | 'confirmed'; // 草稿、已确认
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface SalesDeliveryItem {
  id: number;
  sales_delivery_id: number;
  product_id: number;
  product_code?: string; // 冗余字段
  product_name?: string; // 冗余字段
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  updated_at: string;
}

export interface SalesDeliveryCreateInput {
  sales_order_id: number;
  delivery_date: string;
  remark?: string;
  items: {
    product_id: number;
    quantity: number;
    unit_price: number;
  }[];
}

// 生产计划相关类型
export interface ProductionPlan {
  id: number;
  plan_no: string; // 生产计划号
  product_id: number;
  product_code?: string; // 冗余字段
  product_name?: string; // 冗余字段
  planned_quantity: number; // 计划生产数量
  actual_quantity?: number; // 实际完成数量
  plan_date: string; // 计划日期
  start_date?: string; // 开始日期
  completion_date?: string; // 完成日期
  status: 'draft' | 'approved' | 'in_progress' | 'completed' | 'cancelled'; // 草稿、已审核、进行中、已完成、已取消
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductionPlanItem {
  id: number;
  production_plan_id: number;
  material_id: number;
  material_code?: string; // 冗余字段
  material_name?: string; // 冗余字段
  required_quantity: number; // 需要数量
  consumed_quantity?: number; // 已消耗数量
  created_at: string;
  updated_at: string;
}

export interface ProductionPlanCreateInput {
  product_id: number;
  planned_quantity: number;
  plan_date: string;
  remark?: string;
  materials: {
    material_id: number;
    required_quantity: number;
  }[];
}

export interface ProductionPlanUpdateInput {
  product_id?: number;
  planned_quantity?: number;
  plan_date?: string;
  start_date?: string;
  completion_date?: string;
  status?: 'draft' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
  remark?: string;
}

// 生产完工相关类型
export interface ProductionCompletion {
  id: number;
  completion_no: string; // 完工单号
  production_plan_id: number;
  production_plan_no?: string; // 冗余字段
  product_id: number;
  product_code?: string; // 冗余字段
  product_name?: string; // 冗余字段
  completed_quantity: number; // 完工数量
  completion_date: string;
  status: 'draft' | 'confirmed'; // 草稿、已确认
  remark?: string;
  created_at: string;
  updated_at: string;
}

export interface ProductionCompletionItem {
  id: number;
  production_completion_id: number;
  material_id: number;
  material_code?: string; // 冗余字段
  material_name?: string; // 冗余字段
  consumed_quantity: number; // 消耗数量
  created_at: string;
  updated_at: string;
}

export interface ProductionCompletionCreateInput {
  production_plan_id: number;
  completed_quantity: number;
  completion_date: string;
  remark?: string;
  materials: {
    material_id: number;
    consumed_quantity: number;
  }[];
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 分页相关类型
export interface PaginationQuery {
  page?: number;
  limit?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
