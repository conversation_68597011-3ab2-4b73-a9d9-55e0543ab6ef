import express from 'express';
import { 
  getPurchaseReceipts, 
  createPurchaseReceipt, 
  getPurchaseReceipt
} from '../controllers/purchaseReceiptController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/purchase-receipts - 获取采购入库单列表
router.get('/', getPurchaseReceipts);

// GET /api/purchase-receipts/:id - 获取单个采购入库单详情
router.get('/:id', getPurchaseReceipt);

// POST /api/purchase-receipts - 创建采购入库单
router.post('/', createPurchaseReceipt);

export default router;
