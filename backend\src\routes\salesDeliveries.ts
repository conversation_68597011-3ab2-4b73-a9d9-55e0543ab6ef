import express from 'express';
import { 
  getSalesDeliveries, 
  createSalesDelivery, 
  getSalesDelivery
} from '../controllers/salesDeliveryController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/sales-deliveries - 获取销售出库单列表
router.get('/', getSalesDeliveries);

// GET /api/sales-deliveries/:id - 获取单个销售出库单详情
router.get('/:id', getSalesDelivery);

// POST /api/sales-deliveries - 创建销售出库单
router.post('/', createSalesDelivery);

export default router;
