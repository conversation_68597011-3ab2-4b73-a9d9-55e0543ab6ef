"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const productionCompletionController_1 = require("../controllers/productionCompletionController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/production-completions - 获取生产完工单列表
router.get('/', productionCompletionController_1.getProductionCompletions);
// GET /api/production-completions/:id - 获取单个生产完工单详情
router.get('/:id', productionCompletionController_1.getProductionCompletion);
// POST /api/production-completions - 创建生产完工单
router.post('/', productionCompletionController_1.createProductionCompletion);
exports.default = router;
//# sourceMappingURL=productionCompletions.js.map