"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const productionPlanController_1 = require("../controllers/productionPlanController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/production-plans - 获取生产计划列表
router.get('/', productionPlanController_1.getProductionPlans);
// GET /api/production-plans/:id - 获取单个生产计划详情
router.get('/:id', productionPlanController_1.getProductionPlan);
// POST /api/production-plans - 创建生产计划
router.post('/', productionPlanController_1.createProductionPlan);
// POST /api/production-plans/:id/approve - 审核生产计划
router.post('/:id/approve', productionPlanController_1.approveProductionPlan);
// POST /api/production-plans/:id/start - 开始生产
router.post('/:id/start', productionPlanController_1.startProduction);
// PUT /api/production-plans/:id/status - 更新生产计划状态
router.put('/:id/status', productionPlanController_1.updateProductionPlanStatus);
// DELETE /api/production-plans/:id - 删除生产计划
router.delete('/:id', productionPlanController_1.deleteProductionPlan);
exports.default = router;
//# sourceMappingURL=productionPlans.js.map