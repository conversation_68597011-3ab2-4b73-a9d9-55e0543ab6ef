import express from 'express';
import { 
  getSalesOrders, 
  createSalesOrder, 
  getSalesOrder,
  approveSalesOrder,
  updateSalesOrderStatus,
  deleteSalesOrder
} from '../controllers/salesOrderController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// GET /api/sales-orders - 获取销售订单列表
router.get('/', getSalesOrders);

// GET /api/sales-orders/:id - 获取单个销售订单详情
router.get('/:id', getSalesOrder);

// POST /api/sales-orders - 创建销售订单
router.post('/', createSalesOrder);

// POST /api/sales-orders/:id/approve - 审核销售订单
router.post('/:id/approve', approveSalesOrder);

// PUT /api/sales-orders/:id/status - 更新销售订单状态
router.put('/:id/status', updateSalesOrderStatus);

// DELETE /api/sales-orders/:id - 删除销售订单
router.delete('/:id', deleteSalesOrder);

export default router;
