"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCustomers = getCustomers;
exports.getCustomer = getCustomer;
exports.createCustomer = createCustomer;
exports.updateCustomer = updateCustomer;
exports.deleteCustomer = deleteCustomer;
const database_1 = require("../models/database");
// 获取客户列表
async function getCustomers(req, res) {
    try {
        const { page = 1, limit = 10, search = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE status = 'active'";
        const params = [];
        if (search) {
            whereClause += " AND (code LIKE ? OR name LIKE ? OR contact_person LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM customers ${whereClause}`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT * FROM customers ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
        const customers = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: customers,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取客户列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取客户列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个客户
async function getCustomer(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        const customer = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM customers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!customer) {
            return res.status(404).json({
                success: false,
                message: '客户不存在'
            });
        }
        res.json({
            success: true,
            message: '获取客户成功',
            data: customer
        });
    }
    catch (error) {
        console.error('获取客户错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建客户
async function createCustomer(req, res) {
    try {
        const { code, name, contact_person = '', phone = '', address = '', credit_limit = 0 } = req.body;
        // 验证必填字段
        if (!code || !name) {
            return res.status(400).json({
                success: false,
                message: '客户编码和名称不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 检查编码是否已存在
        const existingCustomer = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM customers WHERE code = ?', [code], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (existingCustomer) {
            return res.status(409).json({
                success: false,
                message: '客户编码已存在'
            });
        }
        // 创建客户
        const customerId = await new Promise((resolve, reject) => {
            db.run(`INSERT INTO customers (code, name, contact_person, phone, address, credit_limit)
         VALUES (?, ?, ?, ?, ?, ?)`, [code, name, contact_person, phone, address, credit_limit], function (err) {
                if (err)
                    reject(err);
                else
                    resolve(this.lastID);
            });
        });
        res.status(201).json({
            success: true,
            message: '客户创建成功',
            data: { id: customerId }
        });
    }
    catch (error) {
        console.error('创建客户错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 更新客户
async function updateCustomer(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const db = (0, database_1.getDatabase)();
        // 检查客户是否存在
        const existingCustomer = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM customers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingCustomer) {
            return res.status(404).json({
                success: false,
                message: '客户不存在'
            });
        }
        // 如果更新编码，检查是否重复
        if (updateData.code && updateData.code !== existingCustomer.code) {
            const duplicateCustomer = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM customers WHERE code = ? AND id != ?', [updateData.code, id], (err, row) => {
                    if (err)
                        reject(err);
                    else
                        resolve(row);
                });
            });
            if (duplicateCustomer) {
                return res.status(409).json({
                    success: false,
                    message: '客户编码已存在'
                });
            }
        }
        // 构建更新语句
        const updateFields = [];
        const updateValues = [];
        Object.entries(updateData).forEach(([key, value]) => {
            if (value !== undefined) {
                updateFields.push(`${key} = ?`);
                updateValues.push(value);
            }
        });
        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有提供更新数据'
            });
        }
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateValues.push(id);
        await new Promise((resolve, reject) => {
            db.run(`UPDATE customers SET ${updateFields.join(', ')} WHERE id = ?`, updateValues, (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '客户更新成功'
        });
    }
    catch (error) {
        console.error('更新客户错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 删除客户（软删除）
async function deleteCustomer(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查客户是否存在
        const existingCustomer = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM customers WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingCustomer) {
            return res.status(404).json({
                success: false,
                message: '客户不存在'
            });
        }
        // 软删除
        await new Promise((resolve, reject) => {
            db.run('UPDATE customers SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['inactive', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '客户删除成功'
        });
    }
    catch (error) {
        console.error('删除客户错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=customerController.js.map