"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const salesDeliveryController_1 = require("../controllers/salesDeliveryController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/sales-deliveries - 获取销售出库单列表
router.get('/', salesDeliveryController_1.getSalesDeliveries);
// GET /api/sales-deliveries/:id - 获取单个销售出库单详情
router.get('/:id', salesDeliveryController_1.getSalesDelivery);
// POST /api/sales-deliveries - 创建销售出库单
router.post('/', salesDeliveryController_1.createSalesDelivery);
exports.default = router;
//# sourceMappingURL=salesDeliveries.js.map